A bearing fault data augmentation method based on hybrid-diversity loss
diffusionmodelandparameter transfer
YuanWeia,ZhijunXiaoa,XiangyanChena,b,*,XiaohuiGuc,Kai-UweSchroderd
aSchoolofMechatronic Engineering andAutomation, Shanghai University, Shanghai 200444, China
bProvincial KeyLaboratory ofMultimodal Perceiving andIntelligent Systems, JiaxingUniversity, Jiaxing314001, China
cStateKeyLaboratory ofMechanical Behavior andSystemSafetyofTrafficEngineering Structures, Shijiazhuang TiedaoUniversity, Shijiazhuang 050043, China
dInstituteofStructural Mechanics andLightweight Design,RWTHAachenUniversity, Wüllnerstra ße7,52062Aachen, Germany
ARTICLE INFO
Keywords:
Dataaugmentation
Diffusionmodel
CFGDMHD
FaultdiagnosisABSTRACT
Thefaultdiagnosisofmechanical equipment canpreventpotentialmechanical failures,avoidpropertydamage
andpersonalinjury,andensurethestableandsafeoperationofmechanical equipment. Datadrivenisan
importantaspectofintelligent faultdiagnosis.Whendataisscarce,itcanseriouslyaffecttheaccuracyoffault
diagnosisandmakeitdifficulttoensurethesmoothandsafeoperationofmachinery. Facedwiththischallenge,a
classifier-free guidancediffusionmodelcombining hybridlossanddiversityloss(CFGDMHD) isproposedfor
dataaugmentation offaultsamples.Thisnewdataaugmentation methodgeneratessampleswiththesamedata
distribution asrealsamplesfromrandomnoisethroughdiffusionprocess.CFGDMHD cangeneratemulti-class
samplessimultaneously withouttheneedforadditional classifierguidanceinthejointtrainingofuncondi-
tionaldiffusionmodelsandconditional diffusionmodels.Thisworkproposesdiversitylosstoimprovethedi-
versityofgeneratedsamples.Weconducted experiments usingabearingdataset.Theresultsindicatethatthe
samplequalityanddiversitygeneratedbythismethodareexcellent,whichcanhelpimprovetheaccuracyof
faultdiagnosisandensurethesafeoperationofmechanical systems.
1.Introduction
Inmoderndailyproduction andlife,manysituationsrequiretheuse
ofmechanical equipment [1].Withtheoperationofmechanical equip-
ment,damageinevitably occursduetovariousreasons.Mechanical
equipment canoperatemoreefficiently, needlessmaintenance, and
havealongerservicelifeifmechanical faultsarepreventedinadvance
[2-4].Inthefieldsofenergy,transportation, manufacturing, aviation,
etc.,mechanical faultdiagnosisplaysanextremely important rolein
modernindustry[5,6].Bearingsarethemostcommonvulnerable parts
inmechanical equipment, withahighfailurerate.Bearingfailurecan
leadtomechanical equipment vibration,increasednoise,andreduced
efficiencyofmechanical equipment [7,8].Reference [9]statesthat
bearingproblemsaccountforaround30%ofmechanical equipment
failures.Therefore, conducting researchinthefieldofbearingfault
diagnosisisofgreatsignificance forthesmoothandsafeoperationof
mechanical systems.
Inthepastdecades,traditional faultdiagnosismethodshavebeenanimportant researchdirectionforscholars.Ithasbeenwidelyusedin
production anddailylife,ensuringthesafeoperationofmanyequip-
mentandreducingcasualties.Theresearchontraditional faultdiagnosis
methodsreliestoacertainextentonthestudyoffaultmechanisms,
whichrequiresalargeamountofexpertknowledge, anditisvery
difficulttoaccurately modelandanalyzethefaultmechanisms oflarge
mechanical equipment [10-12].Withtheincreasing researchonintel-
ligence,mechanical faultdiagnosistechnology isalsodeveloping to-
wardsintelligence. Intelligent faultdiagnosisreferstotheabilityto
automatically diagnoseandclassifymechanical faultswithoutprior
knowledge [13].Intelligent mechanical faultdiagnosismainlyutilizes
bigdata-driven deeplearningmodels,usestheexcellentfeature
extraction abilityofdeeplearningtoautomatically minefaultinfor-
mationinalargeamountoffaultdata,identifyhiddenfaultpatterns,
improvediagnostic accuracy,andachieveend-to-end faultdiagnosis
[14].
Atpresent,therehavebeenmanyresearchachievements infault
diagnosisbasedondeeplearning.Hanetal.[15]combinedCNNwith
*Corresponding author.
E-mailaddresses:<EMAIL> (Y.Wei),<EMAIL> (Z.Xiao),<EMAIL> (X.Chen),<EMAIL> (X.Gu),kai-uwe.schroeder@
sla.rwth-aachen.de (K.-U.Schroder).
ContentslistsavailableatScienceDirect
Reliability Engineering andSystemSafety
u{�~zkw s{yo|kro> ÐÐÐ1ow�o �to~1m{y2w{m k�o2~o��
https://doi.org/10.1016/j.ress.2024.110567
Received20April2024;Receivedinrevisedform10August2024;Accepted5October2024Reliability  Engineering  and System  Safety  253 (2025)  110567
Available  online  6 October  2024
0951-8320/©  2024  Elsevier  Ltd. All rights  are reserved,  including  those  for text and data mining,  AI training,  and similar  technologies.

theproposeddatapreprocessing method,utilizedCNNtoextract
high-levelfeaturesfromreconstructed signalsforintelligent diagnosis,
andachievedgoodfaultdiagnosisresults.Daoetal.[16]proposeda
BO–CNN-LSTM faultdiagnosismodelforhydraulic turbines,and
appliedBayesianoptimization totheCNN-LSTM diagnosismodelto
solvethemodelhyperparameter selectionproblem.Amultiscale trans-
formerCNNmodelwasproposedbyZhuetal.[17],whichintegrated
localandglobalinformation andhasgooddiscriminative abilityforfault
data.Theabove-mentioned modelshaveachievedsatisfactory fault
diagnosisresults,effectively assistinginthereliableandstableoperation
ofequipment. However,theyallhaveanimportantprerequisite, which
istheneedforalargeamountofdataformodeltraining.Infact,me-
chanicalfaultdataisscarce,andobtainingalotofhigh-quality available
datarequiresahighcost.Undertheconditionofscarcefaultdata,fault
diagnosismodelsareoftendifficulttotrain,makingitdifficulttoach-
ievedesiredfaultdiagnosisresults.Therefore, itisnecessarytousefault
diagnosismethodswithfew-shotdata.
Onepracticalsolutiontothelackofqualitydataisdataaugmenta-
tion.Thetwobroadcategories ofdataaugmentation techniques are
generative model-based dataaugmentation techniques andclassical
dataaugmentation techniques. Traditional dataaugmentation methods
includeoversampling, down-sampling, anddatareconstruction. Tradi-
tionaldataenhancement methodsleadtotheproblemthatthedataset
containsplentyofduplicatesamplesorthedatasetcoverageissmall[18,
19].Thecurrentlypopulargenerative modelsfordataaugmentation are
generative adversarial network(GAN),variational autoencoder (VAE),
andtheirvariants.Sunetal.[20]proposedaWasserstein distanceand
auxiliaryclassification generative adversarial network(WAC-GAN) for
expanding faultdataofnewenergyvehicles,achievingsatisfactory fault
diagnosisaccuracy.Con-GANdataaugmentation wasproposedbyDai
etal.[21]asawaytoaugmentthedatasetthroughthecreationofreal
continuations ofthesignalsthatalreadyexistandtheirintegration with
theoriginalsignals.Guoetal.[22]increasedthenumberofGANgen-
erators,thegenerators werecategorized intomultiplegroupsandeach
groupofgenerators sharedsomeoftheweightparameters, thepatternof
multiplegenerators reducesthepatterncollapseoftheGANmethodto
someextent.Two-Stage GANwasproposedbyLuoetal.[23],which
mainlyovercame theproblemsoftraditional GANbeingunableto
extracttimeseriesinformation fromsequencedataandunableto
generatemultipletypesofdatasimultaneously. Inordertoachieve
samplegeneration andanomalous generation samplefiltering,Shao
etal.[24]proposedaGANmodelwithfilteringmechanism. Thismodel
incorporated self-attention mechanism andinstancenormalization into
theGANstructure,ensuringthegeneration ofhigh-quality samples.
Zhongetal.[25]enhanced thequalityofgenerated samplesby
combining DCGANwithspectralnormalization andaself-attention
module.Yanetal.[26]combined VAEandconditional Wasserstein
GANwithgradientpenalty(CWGAN-DP) tointegrateVAEand
CWGAN-DP samplegeneration capabilities tosolvetheproblemoflack
ofdataforthechillerautomated faultdiagnosis.Designedanimproved
auxiliaryclassifierGANsupervised modelinreference[27],addinga
classifiermodeltotheauxiliaryclassifierGAN(ACGAN)framework to
solvetheproblemofgenerating multi-class samplessimultaneously.
Furthermore, theWasserstein distanceisincorporated toenhancethe
lossfunctionandsurmountthechallengeoftrainingthemodel.In
general,therehavebeensomeresearchresultsondataaugmentation
methodsforintelligent faultdiagnosis, andtheseliteratures hascon-
ductedin-depthresearchonGANandVAEmodels.However,thereare
stillseveralissueswiththeseresearchmethods.
(1)GAN-based dataaugmentation methodscannotgetridofthe
adversarial trainingprocess,whichcanleadtopatterncollapse
andunstabletraining,thatis,ittendstogeneratesimilarsamples
andtheproblemofdifficultfittingduringtraining.
(2)TheGANmodelrequirestrainingagenerative modelforeach
classofsamples,whichincreasesthetrainingburden.AlthoughACGANintroduces classifierguidancetogeneratemulti-class of
samplesatthesametime,thequalityoftheclassifierinterferes
withthequalityofthegeneratedsamples.
(3)ThelatentspaceofVAEisfinitedimensional, makingitdifficult
tocapturecomplexdatadistributions.
Inrecentyears,diffusionmodelshavegainedagreatreputation in
thefieldofartificialintelligence generation. Researchhasshownthat
diffusionmodelsmaybesuperiortoGANs.Diffusionmodelscan
generatehigh-quality anddiverseimages,andtheirrobustness isalso
particularly excellent[28].Theconceptofdiffusionhasbeenappliedin
multiplefields.Nowadays, thetheoryofthermodynamics isthesource
ofdiffusionmodelsinthefieldofartificialintelligence, wherediffusion
isthemovement ofmoleculesfromareasofhighconcentration toareas
oflowconcentration. Dicksteinetal.[29]depictedthediffusionprocess
asaMarkovchainandusedaniterativeforwarddiffusionprocessto
progressively changethedatadistribution intoaknowndistribution
beforecreatingnewdatafromrandomnoiseusingareversediffusion
process.TheDenoising DiffusionProbabilistic Models(DDPMs)pro-
posedbyHo[30]layasolidfoundation forthedevelopment ofdiffusion
modelsinthefieldofartificialintelligence, whichcombinediffusion
probability modelswithLangevindynamicmatchingdenoisingscoresto
designvariational lowerboundlossformodeltraining.The
Classifier-free guidancediffusionmodel(CFGDM)proposedinreference
[31]cangeneratemulti-class samplessimultaneously withoutclassifier
guidance.Thesestudiesallindicatethatdiffusionmodelshaveexcellent
generative capabilities.
Inthisarticle,weproposeaCFGDMHD modelimprovedbyCFGDM
forthetaskofexpanding faultdiagnosissamples,providingrichtraining
dataforintelligent faultdiagnosismethodsandensuringthesafeand
stableoperationofmechanical systems.Compared withexistingdata
augmentation methods,CFGDMHD hasmanyadvantages. Firstly,
CFGDMHD hasbettergeneration stabilityduetoitsdiffusionprocess,
whicheffectively avoidstheproblemsofpatterncollapseandpattern
noisethatmayoccurinGANbaseddataaugmentation methods.This
stablegeneration processensuresthatthegenerated sampleshave
higherquality.Secondly,thegeneration processofCFGDMHD isbased
onaseriesofdiffusionsteps,whichcanbedescribedbypartialdiffer-
entialequations, makingthemodelgeneration processmoreintuitive
andinterpretable, thusenhancing thecredibility ofthemodel.Thirdly,
CFGDMHD achievesthesimultaneous generation ofmultipleclass
sampleswithouttheneedforexternalclassifiers. Compared withdata
augmentation methodsthatrequireexternalclassifiers toguidethe
generation ofclasses,iteliminates theadversarial attackeffectcaused
byexternalclassifiersandreducestheburdenoncomputing resources.
Fourthly,CFGDMHD adoptsanimprovedlossfunction,whereHDloss
ensuresthegeneration ofrealisticsampleswhilemaintaining excellent
diversity,resultinginrichandhigh-quality generatedsamples.
Overall,weproposeageneration modelformechanical faultsam-
ples,whereCFGDMHD effectively overcomes theshortcomings ofdata
augmentation methodsbasedonGANandVEA,andgeneratessamples
withbetterquality.Thefollowing arethispaper’sprimary
contributions.
(1)TheproposedCFGDMHD modelprovidesanewsolutionforfew-
shotdataproblemsinthefieldofmechanical faultdiagnosis.
Breakingawayfromdependence onGAN,themodeltraining
processiseasiertofit.Thesamplegeneration processiseasierto
interpret,whichenhancesthecredibility ofdataaugmentation
methodsinreal-lifeproduction applications.
(2)Wehaveimprovedthelossfunctionbyaddingdiversitylosson
thebasisoftheoriginallossfunction,whichenhancesthedi-
versityofgeneratedsamplesandisbeneficialforimproving the
generalization performance offaultdiagnosismodels.
(3)CFGDMHD cangeneratemulti-class ofsamplessimultaneously
withouttheguidanceofanexternalclassifier,withouttheneedtoY.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
2

retrainthemodelforeachclassofsamples.Theabsenceofan
externalclassifierisalsobeneficialforimproving thequalityof
generatedsamples.
(4)Wepre-traintheCFGDMHD modelondatasetswithalarge
amountofdata,andthenfine-tuning itonfew-shotdatasets.
Generative modelsalsobelongtothecategoryofdeeplearning,
andusingparametertransfertechnology ofpre-training andfine-
tuningcanhelpimprovethelimitations offew-shotdatasetson
generative models.
(5)Theproposedmethodcaneffectively enhancetheperformance of
faultdiagnosismodels,enablingthemtopredictanddiagnose
mechanical equipment failuresmoreaccurately andreliably,
therebyensuringthesmoothandsafeoperation ofthe
equipment.
Thesecondsectionofthispaperwillintroducetherelevantknowl-
edgeofdiffusionmodels.Ourproposedmethodisdescribedindetailin
thethirdsection.Thefourthsectionisourexperimental content,andthe
fifthsectionistheconclusion.
2.Background knowledge
2.1.DDPMs
Inrecentyears,diffusionmodelshavereceivedwidespread attention
duetotheirhigh-quality generation abilityandflexibility.Thesemodels
haveachievedsignificant resultsinimagegeneration, videogeneration,
andotherfields,capableofgenerating highlyrealisticimageandvideo
content.DDPMswerefirstproposedin2020.Simplyput,theyarea
generative modelthatcangeneratesamplesfromnoisebycontinuously
addingGaussiannoisetothetrainingdata,andthenlearningtoreverse
thisnoiseprocesstorecoverthedata.Aftertraining,DDPMscanbeused
togeneratedatabypassingrandomlysamplednoisethroughalearned
denoisingprocess.Specifically, theprincipleofDDPMsisshowninFig.1
[30].Next,theDDPMswillbeintroduced fromthreeaspects:forward
process,backwardprocess,andoptimization objectives.
2.1.1.Forward process
Theforwarddiffusionprocessistheprocessofcontinuously adding
noisetoknowncomplexdata,ultimately obtainingasimple,isotropi-
callyindependent Gaussiandistribution data.Specifically, itdoesthis
usingaMarkovchainthataddsTstepsofsmallamountsofnoisefrom
theactualdatax0⊃qxtoproduceaseriesofdatasamples x1Cx2Cx3C
…CxTwithnoise.WhenTapproaches infinity,theobtaineddataisan
independent Gaussiandistribution inalldirections. Ineachstepofthe
forwardprocess,weaddGaussiannoisewithvariance βttoxt 1to
produceanewhiddenvariable xtwithdistribution qxt†xt 1,andthe
equationforthediffusionprocessisshownbelow.
qxt†xt 1N(
xtCut⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪
1 βt⇓
xt 1Cβt⋅I)
⊥t∃⊔1C2C…CT⊓ (1)
whereTisthenumberofdiffusionsteps; βt∃0C1isthediffusionrate
atstept.βtisahyperparameter whichcanbekeptconstantorlearned
online;Iisaunitmatrixwiththesamedimension astheinputsamples
x0;andNxCμCσdenotesaGaussiandistribution withmean μand
variance σ.Thankstothereparameterization tricktheaboveprocesscanalsobeexpressedasfollows.
xt⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪
1 βt⇓
xt 1βtzt 1 (2)
Thatis,combining xt *********************************** zt 1⊃
N0CI,wherezx μEσ.Letαt1 βtCαt̃T
i1αi,andthe
combinedGaussiandistributions zt 2Czt 3C…C⊃N0CI.Therefore, we
canobtainanyxtdirectlyfromx0withoutastep-by-step iteration,
expressedasfollows.
xt⎪⎪⎪⎪
αt⇓
x0⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪
1 αt⇓
zt (3)
qxt†x0NxtCμt⎪⎪⎪⎪
αt⇓
x0C1 αt⋅I⊥t∃⊔1C2C…CT⊓ (4)
2.1.2.Reverseprocess
Thegoalofthereverseprocessistodenoisetheknownisotropically
independent Gaussiandistributed datastepbysteptoobtainasynthetic
data,whichisthereverseoftheforwardprocessandcanbedenotedas
qxt 1†xtCt∃⊔TCT 1C…C1⊓.AccordingtoBayesiantheorem,weobtain
theposteriorprobability distribution qxt 1†xtCx0asfollows.
qxt 1†xtCx0Nxt 1C}μtxtCx0C}βtI (5)
where}utxtCx0and}βtaredefinedintermsofx0Cxt.Theirspecificex-
pressionsareasfollows.
}utxtCx0⎪⎪⎪⎪⎪⎪⎪⎪⎪αt 1♠βt
1 αtx0⎪⎪⎪⎪αt♠1 αt 1
1 αtxt (6)
}βt1 αt 1
1 αtβt (7)
Itisusuallydifficulttofindtheparameters ofqxt 1†xt,butthanksto
thecurrentrapidriseofneuralnetworks,itispossibletoapproximate
thereversedistribution usingaU-netbasedneuralnetworkasfollows.
pθxt 1†xtNxt 1CμθxtCtCΣθxtCt (8)
Finally,wecantrainthenetworktopredictx0.Eqs.(4)and(6)can
becombined toderivetheparameterized reversedistribution mean
μθxtCtasfollows.
μθxtCt1⎪⎪⎪⎪αt♠[
xt βt⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪1 αt♠ ϵθxtCt]
(9)
Ingeneral,thereverseprocessisastep-by-step de-noising processto
getthefinalsynthesized data,andsincetheparameters oftheinverse
processarenotknown,aneuralnetworkisusedtopredicttheparam-
eters ϵθxtCt.
2.1.3.Optimization objectives
Theoretically, weneedtousethemodeltopredictparameters
μθxtCtand ΣθxtCt.Thedeeplearningmodelistrainedbyminimizing
thecross-entropy lossfunctionbetween qx0andpθx0.Forthecon-
venienceofhandling pθx0,thelossfunctionmentioned aboveis
replacedbyminimizing thevariational lowerboundlossasfollows.
LVLBEqx0BT⌊
logqx1BT†x0
pθx0BT⌋
≽ Eqx0logpθx0 (10)
Fig.1.ForwardprocessandreverseprocessofDDPMs.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
3

whereqx1BT†x0̃T
t1qxt 1†xt.Combined withtheBayesian
formulation, thevariational lowerboundlossfunctioncanagainbe
simplifiedasfollows.
Ho[30]proposedthatthevariance ΣθxtCtbefixedtoaconstant σ2I,
where σ2βt.AscanbeseeninEq.(9),themeanhasbeenrewrittenas
afunctionrelatedtothenoise ϵθxtCt.Inotherwords,thetaskofthe
deeplearningmodelcanbetopredictthenoise.Itcanbeconcludedthat
DKLqxT†x0‡pθxTandlogpθx0†x1areconstants.Furthersimplifi-
cationofthevariational lowerboundlossfunctioncanyieldasimplified
lossfunctionfortrainingdeeplearningmodels.Theequationisas
follows.
LsimplyθEtCx0Cϵ√
‡ϵ ϵθ(⎪⎪⎪⎪
αt⇓
x0⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪
1 αt⇓
ϵCt)
‡2⇑
(12)
where ϵisthetruenoise; ϵθisthenoisepredictedbythedeeplearning
model.
Throughtheaboveanalysis,itisnotdifficulttofindthatDDPMs
graduallyaddnoisetothedataandlearnthereverseprocesstorestore
theoriginaldata.Thismechanism avoidsdirectconfrontation between
thegeneratoranddiscriminator inGAN,therebyreducingthepossibilityofgradientvanishingandexploding. Inaddition,DDPMsusevariational
inferenceforoptimization, whichhelpsmaintainthestabilityofthe
trainingprocess.Thediffusionmodelisanexplicitmodelthatrestores
databygraduallyaddingnoisetothedataandlearningthereverse
process.Thismechanism hasaclearermathematical foundation and
optimization objectives, makingDDPMsmoreinterpretable inthepro-
cessofgenerating data.
2.2.CFGDM
DDPMscangeneratehigh-quality anddiversesamples,butthe
samplestheygenerateareuncontrollable. Inmanyfields,itisnecessary
togeneratespecificsamplestoensurethatthegeneratedsampleshavea
certaindegreeofcontrollability. Classifierguidancediffusionmodel
(CGDM)proposedbyDhariwal[28]introduces labelsforconditional
generation, whichresultsinahigherfidelityoftheimagesgeneratedby
themodel.Specifically, itisanimproveddiffusionmodelbyutilizinga
classifier py†x.Aclassifier py†xtCtistrainedonanoisysamplext,and
thenagradient⋈xtlogpy†xtCtisusedtoguidethediffusionsampling
processtowardsthecategory y.Thediffusionprocessisthenusedasan
inverseprocesstoguidethediffusionsamplingprocess.Theinverseof
thediffusionprocesscanbeexpressedasfollows.
Fig.2.Lintra classandLinter classB
LVLBEq⌈
DKLqxT†x0‡pθxT̂T
t2DKLqxt 1†xtCx0‡pθxt 1†xt logpθx0†x1⌉
(11)Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
4

pxt 1†xtCyN(
μθŝ
⋈logpy†xtCtCΣθ)
(13)
wheresistheclassifierguidancescale,thelargershelpsthemodelto
focusmoreontheclassifier.However,therearesomeproblemswith
CGDMusingexplicitclassifierstoguideconditional generation:
(1)Anadditional noisyversionoftheclassifierneedstobetrained,
whichmakesthetrainingprocessmorecomplicated.
(2)Thequalityoftheclassifierwillaffecttheeffectiveness of
generating bycategory.
(3)Updatingtheimagebygradientleadstoanadversarial attack
effect.
CFGDMcaneffectively avoidtheaboveproblemsbyguidingthe
samplingprocessthroughanimplicitclassifier.
CFGDMwasproposedbyscholarJonathanHo[31]fromGoogle.
CFGDMdoesnothaveaclassifier,itistoimprovethediffusionmodelof
ϵθzλCctoachievethesameeffectasCGDM,where ϵθ⋅denotesthe
neuralnetworkthatpredictsthenoise,zλdenotestheimagesamples
withthenoise,andcdenotesthecategorylabels.CFGDMutilizesneural
networkmodels ϵθzλparameterized unconditional diffusionmodel
pθzand ϵθzλCcparameterized conditional diffusionmodelpθz†c.
CFGDMusesthejointtrainingofunconditional andconditional diffu-
sionmodelsinsuchawaythatitcaneventually generate
category-specific noisepredictions asfollows.
√ϵθzλCc1wϵθzλCc wϵθzλ (14)
Compared toCGDM,thereisnogradient⋈logpy†xtCt.
3.Proposed methodframework
Inthecontextofmechanical faultdiagnosis,weemployCFGDMHD
toenhancefaultsamplesandinvestigate novelapproaches tothefew-
shotdataissue.Ourresearchobjectisthetime-frequency imagesofvi-
brationsignalsaftercontinuous wavelettransform(CWT).Vibration
signalsareoftennonlinearandcontainalotofinformation. Itisdifficult
todealwithhiddenfaultfeaturesbydirectlydiagnosing throughvi-
brationsignals.Therefore, manysignalprocessing methodshave
emergedtoextractthemainfeaturesandachievegoodfaultdiagnosis
results.CWTisoneofthem.CWTisaverypowerfulsignalanalysistool
thatcaptureslocalfeaturesandtransientchangesofsignalsbycontin-
uouslyanalyzingtheminbothtimeandfrequencydomains.Thischar-
acteristicgivesCWTsignificant advantages inprocessing non-stationary
signals.Therearemanyexistingliteraturethatusetime-frequency im-
agesastrainingsamples,whichalsoindicatestherationality ofselecting
time-frequency imagesastheresearchobjectinthispaper.Inaddition,
theoretically, anysignalprocessing methodmaychangecertainchar-
acteristics oftheoriginalsignaltosomeextent.Infact,selecting
reasonable parameters canmakethefeaturesthathaveasignificant
impactondiagnosisappear.Evenifsomefeaturesarelostinthe
transformation, itisacceptable aslongastheydonotplayadecisiverole
inthefaultdiagnosisprocess.Overall,itisadvisableforthisarticleto
focusontime-frequency imagesastheresearchobject.
Finally,thissectionwillintroducethesuggestedmethodfromthree
perspectives: newlossfunctionbasedonLhybridandLdiversity,pre-training
andfine-tuning, andgeneralframework.
3.1.Anewlossfunction basedonLhybridandLdiversity
Nicholetal.[32]proposedanoptimization objective Lhybridthat
combines LsimplyandLVLBbyincorporating thevarianceofthereverse
processintothemodelusingasimplehyperparameter trickbasedon
Lsimply.Lhybridcangenerategoodsampleswithfewersamplingsteps,and
thetargetdistribution coverageofgeneratedsamplesislarger.Lsimply
andLVLBareshowninEqs.(11)and(12),whileLhybridisexpressedasfollows.
LhybridLsimplyλ1LVLB (15)
where λ1isahyperparameter selectedbasedonmanualexperience.
Settingitto0.001canavoidLsimplybeingineffective duetoLVLBbeingtoo
large,therebyensuringthatLhybridleadstobetterdatageneration
quality.
Considering thereasonoflargesimilaritybetweentime-frequency
images,wewillincreasethediversityofgeneratedsamplesbyadding
diversitylossLdiversitytothehybridlossfunction Lhybrid.Ldiversityconsists
oftwoparts,Lintra classandLinter class,whichareillustrated inFig.2.
Ldiversityisbasedonbatcherrorbackpropagation toincreasesampledi-
versity.InthereverseprocessofCFGDMHD, thegeneration ofsamples
ofcorresponding categoriesisguidedbycategorylabels,sowecanget
thecategoryinformation ofeachbatchofgeneratedsamples.Thecosine
similaritycanbeusedasameasureoftherelativedistancebetweenthe
generatedsamples√x0.Therefore, wecalculatethecosinesimilaritybe-
tweeneachbatchofgeneratedsamples√x0
0C√x1
0C√x2
0C…C√xn
0(nisthesizeof
thebatchsize),andthenwecangettheintra-class lossLintra classby
averaging them,whichwillmakethedistribution ofthegenerated
samplesinthebatchwider.Whenincreasing thediversityofthewhole
batchofsamples,weconsiderthatitisnecessarytoreducesimilarity
betweencategories, thusincreasingthediversitybetweencategories. In
generating thewholebatchofsamples,werandomlyselectonesample
fromeachclassxClass0CxClass1C…CxClassm(misthenumberofclasses),
andasetoftwosamplestocalculatethecosinesimilarityandaverageto
gettheinter-classlossLinter class.Theabovetheoretically infersthatthe
diversityofgeneratedsamplescanbeincreased, thatis,thegenerated
samplesneedtomaintainapproximate similaritybutcannotbeexactly
thesame,justlikeallwatermelons aresimilarinlengthbuthavemany
differences. Theidealtrainingsamplesetshouldalsobelikethis.Eachof
theabovelossfunctionequationsisexpressedasfollows.
Lintra class⋃⏟
sim(
√xi
0C√xj
0)⇑
⊥iℑj
C2
n(16)
Linter class⋃}
sim 
xClassiCxClassj)〈
⊥iℑj
C2
Classnum(17)
Ldiversityλ2Lintra classλ3Linter class (18)
amongthem,sim⋅represents cosinesimilarity. Bycombining Lhybrid
withLdiversity,anewlossfunction LHDcanbeobtained,whichcanensure
thequalityofgeneratedsampleswhileincreasing theirdiversity.
LHDLhybridLdiversity (19)
LHDLsimplyλ1LVLBλ2Lintra classλ3Linter class (20)
where λ2and λ3arealsohyperparameters, selectedbasedonmanual
experience. Wefoundthatsetting λ2and λ3between0.001 –0.005is
reasonable andcanimprovethediversityofgenerated samples
appropriately.
3.2.Pre-training andfine-tuning
Atpresent,modelpre-training iswidelyusedinmanyfields,and
manyscholarsuserichdatasetstopre-trainmodels,inordertofacilitate
otherscholarstousemodelsfordownstream tasks.Forexample,many
researchers useimageNet dataset[33]topre-trainmodelsand
fine-tuning themfordownstream tasks.Theadvantages ofpre-training
andfine-tuning canbesummarized asfollows:
(1)Generalized featurescanbelearnedthroughpre-training, leading
tofasterconvergence duringtrainingforthetargettask.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
5

(2)Whenthereissufficienttrainingdataduringpre-training, the
modelperformsbetterinthetargettask.
(3)ThePre-trained modelscansolvetheproblemofinsufficient data
targettask.
(4)Becausepre-trained modelscanlearngeneralfeatures,themodel
hasbettergeneralization performance androbustness, reducing
overfitting.
Inthispaper,weproposeCFGDMHD togeneratetrainingsamplesis
tosolvetheproblemoffew-shotdata.However,CFGDMHD stillbelongs
tothecategoryofdeeplearning,andthesizeoftrainingsamplesalso
affectstheperformance ofthemodel.Intheabsenceoftrainingsamples,
wecannotprovidemoresamplesfortrainingtheCFGDMHD model.
FewertrainingsampleswillmakeitdifficultforCFGDMHD toguarantee
thatthegeneratedsamplearecompletely consistent withtheoriginal
samples.Sopre-training andfine-tuning areeffectivewaystoimprove
thequalityanddiversityofgenerated samples.However,duetothe
particularity oftime-frequency images,therearesignificant differences
betweenthemandexistinglargepubliclyavailabledatasets.TheUni-
versityofPaderborn [34]bearingdatasethasrichexperimental data,
andwewillperformpre-training ofCFGDMHD onthisbearingdataset.Subsequent fine-tuning onthepre-trained CFGDMHD modelusinga
few-shotdatasetwillbeperformed, andthefine-tuning processwill
employfewerepochsandasmallerlearningrate.
3.3.Theproposed method
Forthepurposetocreatehigh-quality anddiversetrainingsamples,
wepresentCFGDMandimprovethelossfunctioninthisstudytosolve
thelimitsofthefew-shotdataproblem.Fig.3depictstheoverall
framework ofCFGDMHD, andthefollowingarethespecificphases
involvedinthisprocess.
(1)Collectmechanical vibrationsignalsunderdifferentfaultstates
andconvertthemintotime-frequency imagesthroughCWT.
(2)Improvetheoriginallossfunctionbycombining thediversityloss
andhybridlosstodeveloptheCFGDMHD model.
(3)Usethebearingdatasetwithalargeamountofdatatopre-train
theCFGDMHD model.
(4)Fine-tuning theCFGDMHD modelusingafew-shotfaultdataset.
(5)Ahugenumberofsyntheticsampleswithdistributions resem-
blingthoseofthefew-shotdatasetareproducedbyCFGDMHD.
Fig.3.Overallframework oftheproposedmethod.
Fig.4.Paderborn University BearingDatasetTestrig.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
6

Thequalityanddiversityofthegeneratedsamplesarequantita-
tivelyanalyzedusingthreeindicators, andthequalityanddi-
versityofthegenerated samplesarevisuallyshownthrough
appropriate visualization techniques.
(6)Expandthetrainingsetofthefaultdiagnosismodelwiththe
generated samples,analyzethefaultdiagnosisresults,and
demonstrate theeffectiveness ofthegeneratedsamples.
4.Experiment andresultsanalysis
ThebearingfaultdatasetfromtheUniversity ofPaderborn isusedto
pre-trainthemodel.TheCFGDMHD modelisfurtheroptimized using
thebearingdatasetsfromtheCaseWesternReserveUniversity (CWRU)
BearingLaboratory [35]andthePolytechnic University ofTurin[36].
Bothdatasetsareusedtoverifytheaccuracyandvarietyofthegener-
atedsamplesofthesuggestedCFGDMHD model,aswellastheimpactof
thegeneratedsamplesonthefaultdiagnosismodel ’sperformance. In
thisarticle,threepubliclyavailabledatasetswereused,twoofwhich
wereusedtovalidatetheeffectiveness oftheproposedmethod.After
readingalargeamountofrelevantliterature,wefoundthatmostpapers
validatetheproposedmethodusingtwodatasets.Additionally, the
operatingconditions ofthesetwodatasetsarenotthesame.Sousingtwo
datasetscanverifytheeffectiveness oftheproposedmethod.Theproject
usesanA100-SXM4 –80GBgraphicscardandthePythondeeplearning
library,Pytorch.
4.1.Relatedworkbeforetheexperiment
4.1.1.Description ofpre-training datasets
ThePaderborn University bearingdatasetconsistsofartificially
inducedfailuresandrealdamagefailuresfromaccelerated experiments,
andthetestrigisshowninFig.4.Theartificially induceddamagefaultsareinducedbyelectricdischargemachining (EDM),drillingandelectric
engraving(EE).Wechoose10typesofdataofartificially inducedfaults
forthepre-training ofthemodel,therotationalspeedofthetestrigis
900rpm,theloadtorqueis0.7N⋅m,theradialforceonthebearingsis
1000N,andthesamplingfrequencyofthevibrationdatais64khz.Inthe
processofthedatapre-processing, toensurethateachsamplecoversat
leastonerotationalcycle,weuse1024datapointsasasample,andthe
vibrationsignalsundergoCWTtogetatime-frequency imagesof3×
112×112,with1000samplesineachclass,andthedatasetusedforpre-
traininghasatotalof10,000samples.Thedetailedinformation is
showninTable1,inwhichKA08andKA09havethesamedegreeof
damage,buttheirdamageareasaredifferent,sotheyaredividedinto
twodifferentclassesinreference[34].
4.1.2.Quantitative evaluation indicators forsamplequality
Itiscrucialtoassessthequalityofimagesproducedbygenerative
models,andthisisdonebylookingatthevarietyandqualityofthe
generatedsamples.Inthefieldofmechanical faultdiagnosis,generative
modelsareoftenusedtosolvedataaugmentation tasks.Tothisend,
high-quality anddistinctive synthesized datasetsaretypicallyneeded.
Tobemoreprecise,theimagesshouldhaveahighdegreeofsimilarity
betweenthesyntheticandrealimages,outstanding quality,and
reasonably strongdiversity.Thiswillhelpthefaultdiagnostic model
performbetterintermsofgeneralization andlessenoverfitting. Fig.5
intuitively illustratesthesuperiority andinferiority ofthesynthesized
dataset.Thesynthesized sampleswillbeassessedinthisworkusingthe
structuralsimilarityindexmeasure(SSIM),inceptionscore(IS),and
frechetinceptiondistance(FID).Eachevaluation indicatorhasitsown
strengthsandweaknesses, sothreeindicatorsareusedsimultaneously
forevaluation tomaketheexperimental resultsmoreconvincing. The
specificdescriptions ofthethreeevaluation indicatorsareasfollows.
AnindicatorcalledSSIMisusedtogaugehowsimilarthetwoimages
aretooneanother.Itnotonlyconsidersthebrightness, contrast,and
structuralinformation oftheimage,butalsosimulatesthehumaneye’s
perception oftheimage.Thecalculation ofSSIMisbasedonpixelin-
formation withinthelocalwindow.Firstly,itdividestheimageinto
manysmallregionsandcalculatesthebrightness, contrast,andstruc-
turalmetricsforeachregion.Then,itcombinesthesemetricstoobtain
thesimilarityscoreoftheentireimage.ThefinalSSIMvaluefallsbe-
tween 1and1,whereanumbernear 1denotesasignificant differ-
encebetweenthetwoimagesandavaluecloseto1showsthattheyare
highlysimilar.
ISisanindicatorthatisusedtoassessthequalityofmodelsamples
thatarecreated.IScombinestwokeyaspects:diversityandauthenticity.
Firstly,ISquantifiesthediversityofsamplesbycalculating theentropy
ofthepredicteddistribution ofthegeneratedsamplesontheInception
network.Themoreuniformthedistribution ofcategoriesinthegener-
atedsample,thehighertheISvalue.Secondly, ISmeasuresthe
authenticity ofsamplesbycalculating theexponential meanofthe
categorywiththehighestprobability inthepredicteddistribution. In
otherwords,theISvalueincreaseswiththepredictionprobability ofthe
categorywiththehighestprobability inthegenerated sample.AsaTable1
Detailedinformation ofPUdataset.
Bearing
CodeFault
LocationDamage
MethodExtentof
Damage
(Level)Numberof
SamplesLabel
K005 Normal — — 1000 0
KA01 Outer
RingEDM 1 1000 1
KA05 Outer
RingEE 1 1000 2
KA06 Outer
RingEE 2 1000 3
KA07 Outer
RingDrilling 1 1000 4
KA08 Outer
RingDrilling 2 1000 5
KA09 Outer
RingDrilling 2 1000 6
KI01 InnerRingEDM 1 1000 7
KI05 InnerRingEE 1 1000 8
KI08 InnerRingEE 2 1000 9
Fig.5.Schematic diagramoftheadvantages anddisadvantages ofthesynthesized dataset.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
7

result,ISoffersathoroughevaluation oftheauthenticity anddiversityof
generatedsamples.Thefollowingistheexpression fortheISequation.
ISGexp(
Ex⊃pgDKLpy†x‡py)
(21)
Amongthem,Ex⊃pgrepresents traversing allgenerated samplesto
calculatethemean;DKLP‡Qrepresents KLdivergence, usedtomea-
surethesimilaritybetween PandQ;py†xrepresents theprobability
thatsamplexbelongstoallcategories; pyrepresents themarginal
probability.
FIDisanindicatorthat’susedtoassesshowwellgenerative models
perform,particularly deeplearningmodelslikeGAN.Bycomparing the
featuredistributions ofgeneratedandrealsamples,FIDcalculateshow
similartheyare.Specifically, FIDcharacterizes thedistributions ofthe
generated andrealsamplesusingthemeansandcovariances ofthe
featurerepresentations obtainedintheintermediate layersofapre-
trainedinceptionnetwork.Thecalculation ofFIDinvolvestwosteps:
first,theFrechetdistanceiscomputedbetweenthefeaturedistributions
oftherealsamplesandthegeneratedsamples;Secondly,byconverting
thestatisticalcharacteristics ofthegeneratedsamplesintoamultivar-
iatenormaldistribution, thisdistanceismappedtoafinalFIDscore.
Bettermodelperformance isshownbyalowerFIDvalue,whichbrings
thegenerated sampleclosertotherealsample.Thefollowingisthe
formulausedtocalculateFID.
FID‡μrμg‡2Tr 
ΣrΣg 2 
ΣrΣg)1E2)
(22)
where μrdenotesthemeanoftherealsamplefeatures; μgdenotesthe
meanofthegeneratedsamplefeatures; Σrdenotesthecovariance matrix
oftherealsample;and Σgdenotesthecovariance matrixofthegener-
atedsample.
4.1.3.Briefdescription oftheexperiment
Inthispaper,weprovideacomprehensive evaluation oftheper-
formanceofdataenhancement forCFGDMHD models,andtheexperi-
mentscanbebroadlycategorized intothreeareas.
(1)Qualitative andquantitative assessment ofthediversityand
qualityofthesamplesthatweregenerated. Thethreeindicators
ofSSIM,IS,andFIDbelongtoquantitative evaluation. Using
VGG16tomaprealandgeneratedsamplestohigh-dimensional
featurespaceandusingTSNEtechnology tovisuallydisplay
sampledistribution belongstoqualitative analysis.Itshouldbe
emphasized thatthesamplesusedtotrainVGG16atthistimeare
realsamples,andalargeramountofdataisusedtomakeVGG16
trainbetter.TheTSNEfigurefunctionhereisusedtoevaluatethe
distribution ofrealsamplesandgeneratedsamples.Additionally,
wewillchoosearandomsamplefromeachclassofgeneratedsamples,discoverthenearestneighborsamplesintherealsam-
ples,andclearlyshowthedifferences betweenthegenerated
samplesandtherealsamplestoconfirmthediversityofthe
generatedsamples.
(2)VAEandACGANhavebeenappliedinthefieldoffew-shotme-
chanicalfaultdiagnosis. Wewillcomparetheperformance of
CFGDMHD andthesemodelsindataaugmentation frommultiple
aspects.
(3)Thefew-shotfaultdiagnosisultimately needstobeimplemented
inspecificfaultdiagnosismodels.VGG16,Resnet50,andDen-
seNet121arerecognized benchmark classification models,which
havealotofapplications inthefieldoffaultdiagnosis.Wewill
usethementioned abovedataaugmentation modelstogenerate
sampleextensiontrainingset,throughtheevaluation offault
diagnosisperformance todemonstrate theexcellence ofthe
methodinthispaper.
4.2.Dataaugmentation andfaultdiagnosis experiments usingtheCWRU
bearingdataset
4.2.1.Description andpreprocessing oftheCWRUbearingdataset
Thedatasetusedinthisexperiment wasprovidedbytheCWRU
BearingDataCenter.Theexperimental setupisshowninFig.6.The
selecteddatasetiscollectedbyanaccelerometer withasamplingfre-
quencyof12kHz,withamotorworkingloadof3HPandamotor
operatingspeedof1730rpm.Thedatasetcontains10differenttypesof
bearinghealthconditions, includingatypeofnormaldataandnine
typesoffaultdata.Inthisexperiment, atotalof300samplesforeach
classofrealdataareobtained,ofwhich180samplesarenotinvolvedin
thetrainingofanynetworkmodelandareonlyusedfortestingof
variousexperiments. Inaddition,100sampleswillbeusedfor
CFGDMHD modeltrainingtolearnthefaultcharacteristics ofeachclass
Fig.6.CWRUtestrig.
Table2
Detailedinformation ofCWRUdataset.
Bearing
CodeFault
LocationFaultSize
(inches)Numberof
SamplesLabel
NOR – – 300 0
IRF1 InnerRing 0.007 300 1
BF1 Ball 0.007 300 2
ORF1 OuterRing 0.007 300 3
IRF2 InnerRing 0.014 300 4
BF2 Ball 0.014 300 5
ORF2 OuterRing 0.014 300 6
IRF3 InnerRing 0.021 300 7
BF3 Ball 0.021 300 8
ORF3 OuterRing 0.021 300 9Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
8

ofsamplesandgeneratealargeamountofdatatoexpandthetraining
samples.Table2showsthedetailedinformation ofthebearingdataset
usedintheexperiment. Afterobtainingtheexperimental samples,we
normalized eachsampleandtransformed itinto3×112×112time-
frequencyimagesusingCWT,whereeachvibrationsignalsamplecon-
tains1024datapoints.
4.2.2.Modelparameters andhyperparameter settings
Duringthereversediffusionprocess,CFGDMHD predictsthenoise
distribution ateachstepthroughU-netandgeneratesthedesiredfault
categorysamplesguidedbycategorylabels.TheinputofU-netincludes
samplematrix,diffusionstepsT,andclasslabels.U-netmainlyconsists
ofanencoderandadecoder.Intheencoder,theinputsamplesarefirst
dimensionalized throughaconvolutional layer,followedbythree
attentionresidualdownsampling convolutional modules,wherethe
attentionmechanism takesintoaccountthediffusionstepTandclass
labels.Aftertheencoderencodestheinput,itwillenteratransitionlayer
tointegratetheencodinginformation. Subsequently, thepredictednoise
isdecodedbythedecoder,whichhasthesamenumberofnetworklayers
astheencoder.Theinputofeachlayerincludestheoutputofthepre-
viouslayerandtheoutputofthecorresponding layerontheencoder.
Table3presentsthedetailedparameters ofU-net.
IntheCFGDMHD architecture, itisalsonecessarytosetthenumber
ofdiffusionstepsT,whichcanaffectthequalityofimagegeneration and
trainingtime.Inthisarticle,Tissetto800andtheoptimizerusedfor
trainingisAdam.Atpresent,theselectionofhyperparameters almost
reliesonmanualexperience toset,becausemostdeeplearningmodels
areblackboxoperations, andhyperparameters arealsosetbasedon
results.Thehyperparameters inthisarticlewerefoundthroughexten-
siveexperiments, usingthehyperparameter gridsearchmethodto
quantitatively andqualitatively analyzetheexperimental resultsof
differenthyperparameter combinations. Thequality,diversity,and
computational resourceconsumption ofthegenerated sampleswere
analyzedtofindtheoptimalhyperparameter combination. More
detailedhyperparameter settingsareshowninTable4.4.2.3.Qualityassessment ofgenerated samples
Inthisexperiment, wefirstpre-traintheCFGDMHD model.Afterthe
modelconverges, theCWRUbearingdatasetisfedintothepre-trained
CFGDMHD modelforfine-tuning. Finally,therandomGaussiannoise
samplesareinputintothemodel,andtherequiredsyntheticdataare
obtainedthroughthereversediffusionprocess.Intheentireprocessof
samplegeneration, themethodproposedinthispaperonlyrequirespre-
trainingandfine-tuning ofthemodeltrainingprocesstwice,withoutthe
needtotrainthemodelseparately foreachcategoryoffaultdata.The
amountofdatausedforfine-tuning is100samplesperclass.Fig.7
visuallyillustratesthequalityanddiversityofgenerated samples,as
wellasthehighsimilaritybetweengeneratedsamplesandrealsamples.
Tofurtherdemonstrate thatthegeneratedfaultsamplesofourpro-
posedmodelarelearningthedatadistribution ofrealsamples.Weuse
realsamplestotraintheVGG16,inputonegeneratedsampleandallreal
samplesofeachclasssequentially intothetrainedVGG16,getthe
mappingofthesesamplesinthenetworkfeaturespace,calculatethe
distancebetweenthegenerated samplesandtherealsamples,and
searchforthenearestneighborsofthegeneratedsamplesinthereal
samples.Fig.8showsthegenerated sampleanditsfirst3nearest
neighborsintherealsample.Itcanbeclearlyobservedthatthesample
generated bytheproposedmethodisnotadirectcopyofthereal
sample.Thegeneratedsampleshavesimilarities andsomedifferences
withtherealsamples,reflectingthehighdiversityofthegenerated
samples.
Inadditiontotheaboveintuitiveproofoftheeffectiveness ofour
proposedmodelingenerating samples,wealsoadoptedfeaturevisual-
izationtechnology tovisuallydisplaythedatadistribution characteris-
ticsbetweenrealsamplesandgeneratedsamples.Specifically, wemix
therealandgeneratedsamplesaccordingto1to2,andinputtheminto
thetrainedVGG16networkmodel.ThefeaturesextractedbyVGG16are
visualizedusingTSNEtechnology, andtheresultsareshowninFig.9.
TheVGG16trainedhereusesalargenumberofrealsamplesfortraining,
soitcanlearnthedifferences betweenvarioustypesoffaultsamples.
Thisisonlyconsidering thequalityofgeneratedsamplesandhasnotyet
beenappliedtothescarcityoffaultsamples.Itcanbeseenthatthe
distribution ofgeneratedsamplesandrealsamplesineachcategoryis
basicallyconcentrated inthesamearea,whichconfirmsthatthesam-
plesgeneratedbytheproposedmethodinthispaperarehighlysimilar
totherealsamples.ForsomecategoriesinFig.9,thepoordiscrimina-
tionofsamplesmaybeduetothefactthatVGG16didnotachievethe
bestclassification accuracyonrealsamples,resultinginsomedeviation
infeatureextractionofgeneratedsamples.
Inthispaper,toquantitatively demonstrate theexcellentperfor-
manceoftheproposedmethod,weusethreeindicators, SSIM,IS,andTable3
Detailedparameters ofU-net.
No. Structure Parameter settings
Encoder 1 Inupt Size3×224×224,T,label
2 Conv KernelsNumber128,Size3,Stride1,padding1
3–4 Attentionresidual×2 Withintheresidual,KernelsNumber128,Size3,Stride1,AttentionTrue
5–6 Downsampling Conv×2 KernelsNumber128,Size3&5,Stride2,AttentionTrue
7–8 Attentionresidual×2 Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
9–10 Downsampling Conv×2 KernelsNumber256,Size3&5,Stride2,AttentionTrue
11–12 Attentionresidual×2 Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
13–14 Downsampling Conv×2 KernelsNumber256,Size3&5,Stride2,AttentionTrue
15–16 Attentionresidual×2 Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
Transition layer 16 Attentionresidual Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
17 Attentionresidual Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionFalse
Decoder 18–19 Attentionresidual×2 Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
20–21 Attentionresidual×2 Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
22–23 Upsampling Conv×2 KernelsNumber256,Size3&5,Stride1&2,AttentionTrue
24–25 Attentionresidual×2 Withintheresidual,KernelsNumber256,Size3,Stride1,AttentionTrue
26–27 Upsampling Conv×2 KernelsNumber256,Size3&5,Stride1&2,AttentionTrue
28–29 Attentionresidual×2 Withintheresidual,KernelsNumber128,Size3,Stride1,AttentionTrue
30–31 Upsampling Conv×2 KernelsNumber128,Size3&5,Stride1&2,AttentionTrue
32 Conv KernelsNumber3,Size3,Stride1,padding1
Table4
hyperparameters ofCFGDMHD.
Hyperparameter Numerical Value
LearningRate 0.0001
T 800
Pre-training Epoch 100
Fine-tuning 10
BatchSize 32Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
9

FID,tocomprehensively evaluatethequalityanddiversityofthe
generated samples.Inaddition,ACGANandVAEarewidelyused
generative modelsthatcontribute tosolvinggeneration tasksinvarious
fields.ACGANandVAEareintroduced tocomparewiththeproposed
methodinthispaper,andGuidedDiffusionwillbeusedtoperform
ablationexperiments toshowthattheimprovedlossfunctionandfine-
tuningmethodofCFGDMHD arebotheffective.
InTable5,theSSIMvalueresultsofthesamplesgeneratedbyeach
typeofmodelareshown.TheReal-RealinTable5istheSSIMvaluebetweenrealsamples,whichisdonetoprovideareferencefortheSSIM
valuescalculated byeachmethod.Fig.10visuallyillustratesthedif-
ferencesandconnections inSSIMvaluesamongvariousmethods.Itcan
beclearlyseenfromthegraphthattheSSIMvalueproposedinthispaper
isthehighest,anditslinetrendisclosesttotheSSIMvalueofReal-Real,
indicating thatthegeneratedsamplesbythemethodproposedinthis
paperareverysimilartorealsamples.Table6showstheISandFID
valuesofthefourmethods.OurproposedmethodhasthebestISandFID
valuesof20.65and9.14,respectively. BoththeISvalueandFIDvalue
Fig.7.Generated sampleimagesoftheCWRUdataset.
Fig.8.ThegeneratedsamplesofCWRUdatasetanditstop3nearestneighborsinrealsamples.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
10

considerthefactorofsamplediversity.ThisarticleproposestheHDloss
functiontoimprovethediversityofgeneratedsamples,whichtosome
extentindicatesthattheHDlossfunctionisbeneficialforimproving the
diversityofgenerated samples.Overall,thesamplesgenerated by
CFGDMHD arehighlysimilartotherealsamples,andthegenerated
samplesalsoensureacertaindegreeofdiversity,providingalarge
amountofdatasupportforusingtime-frequency imagesformechanical
faultdiagnosis.
Fig.9.Visualization resultsofthefeaturesofrealandgeneratedsamplesfromamixedCWRUdataset.
Table5
CWRUdatasetwithdifferentmodelsgeneratessampleSSIMvalues.
Label Real-Real ACGAN VAE GuidedDiffusion CFGDMHD
Label00.8895 0.7236 0.6550 0.7121 0.8667
Label10.7484 0.6279 0.5876 0.5947 0.7297
Label20.6496 0.5561 0.5988 0.5330 0.6381
Label30.7265 0.5124 0.6120 0.5811 0.7130
Label40.8072 0.6157 0.6468 0.6890 0.7939
Label50.7689 0.5680 0.6120 0.5460 0.7370
Label60.6938 0.5589 0.5872 0.5663 0.6530
Label70.7557 0.5998 0.6059 0.6233 0.7325
Label80.7979 0.6257 0.6438 0.6971 0.7861
Label90.8247 0.6438 0.6970 0.6585 0.8032
Fig.10.LinegraphsofSSIMvaluesfordifferentgeneratedsamplesintheCWRUdataset.Table6
CWRUdatasetgeneration sampleswithdifferentmodelsISandFIDvalues.
ACGAN VAE GuidedDiffusion CFGDMHD
IS 7.59 7.94 8.72 20.65
FID 20.63 18.79 32.36 9.14Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
11

4.2.4.Analysis offaultdiagnosis resultsbasedondataaugmentation
Inthissection,thesamplesgenerated byACGAN,VAE,Guided
Diffusion,andCFGDMHD willbemixedwithrealsamplesastraining
setsforfaultdiagnosis.WeuseVGG16,Resnet50,andDensenet121 as
diagnostic modelstomaketheexperiment moreconvincing, andalso
comparetheimpactofdataaugmentation ondifferentfaultdiagnosis
models.Depending onthecomposition ofthedataset,thefaultdiagnosis
experiments canbedividedintoatotaloffivegroups.Table7showsthe
specificdetailsofeachgroup,inwhichtheproportion ofgenerated
samplesinthetrainingsetisincreasing, andthetestsetisunchanged.
Duringtheexperiments, inordertominimizetheeffectoferrors,each
experiment isconducted 10timestocalculatetheaccuracyandvariance
offaultdiagnosis.
Theexperimental resultsareshowninTable8.Observing the
experimental results,itcanbeseenthattheaccuracyoffaultdiagnosis
variesgreatlyintheabsenceofgeneratedsamplesinExp.1,withthe
lowestbeingResnet50withanaccuracyof75.32%andthehighest
beingVGG16withanaccuracyof92.32%.Thisisbecausedifferent
modelshavedifferentdemandsonthedata,withthedeepernetwork
structureofResnet50andDensenet121, whicharebothapplicabletothe
datasetwithcomplexsampledatadistribution andcontaining alarge
numberofsamples.VGG16ismoresuitablefordatasetswithhighinter
sampleidentification andfew-shotdata.Thedatasetusedinthis
experiment isahighlydiscriminative datasetwithoutnoiseunderthe
sameoperatingcondition, sodifferentfaultdiagnosismodelsexhibit
significant differences inaccuracy.Astheproportion ofgenerated
samplesinthetrainingsetincreases,theaccuracytrendsofeachdataaugmentation methodinthethreefaultdiagnosismodelsaresimilar,
whichstronglyindicatesthattheexperimental resultsobtainedfromthe
threefaultdiagnosismodelsareeffectiveinevaluating variousdata
augmentation methods.
Furtheranalyzingtheexperimental results,asshowninTable8,the
methodproposedinthispaperperformsoptimallyinexperiments with
differentfaultdiagnosismodelsandtrainingsetcomposition. When
adding50generatedsamplesforeachclassinthetrainingset,thefault
diagnosisaccuracyofusingGuidedDiffusionandCFGDMHD generated
dataaugmentation datasetsincreased. However,thefaultdiagnosis
accuracyofusingVAEandACGANgenerateddataaugmentation data-
setsdidnotchangesignificantly andslightlydecreased, respectively. As
theproportion ofgeneratedsamplesinthetrainingsetincreases,the
faultdiagnosisaccuracyofeachmethodsignificantly increases. In
Exp.4,thedataaugmentation methodproposedinthispaperhasafault
diagnosisaccuracythatis2%to7%higherthanothermethods.In
Exp.5,withonly600samplesgeneratedforeachclassinthetrainingset,
thefaultdiagnosisaccuracyofCFGDMHD dataaugmentation inVGG16,
resnet50,andDenseNet121 are99.22%,99.24%,and98.72%,
respectively, whileothermethodsarebelow96.53.Theexperimental
accuracyofCFGDMHD inExp.5hasslightlyincreasedcompared to
Exp.4,whiletheexperimental accuracyofGuidedDiffusionandACGAN
inExp.5hasdecreased toacertainextentcompared toExp.4.The
experimental accuracyofVAEinExp.5haslessvariationcomparedto
Exp.4.This indicatesthatourproposedmethodgeneratesthebestsam-
plequality,ensuringgooddiagnostic accuracyeveniftherearenoreal
samplesinthetrainingset.Intheabsenceofrealsamplesinthetraining
set,thediagnostic accuracyofothermethodsismoreorlessreduced.
ThisprovesthatHDlossandparameter transfermethodshelpimprove
thequalityanddiversityofgeneratedsamples.
Overall,thegeneratedsamplesofthemethodproposedinthispaper
aremostsimilartotherealsamples,andcanmaintainexcellentfault
diagnosisaccuracyevenwithoutrealsamplesduringfaultdiagnosis
modeltraining.WhentheGuidedDiffusiongeneratessamplesfordata
augmentation, thereisacertainimprovement inthefaultdiagnosis
model,butrealsamplesneedtoparticipate inmodeltrainingtomain-
tainhighaccuracy.Thisindicatesthatourimprovedlossfunctionand
fine-tuning methodareeffectiveinimproving thequalityanddiversity
ofgeneratedsamples.Inaddition,usingACGANfordataaugmentation
hastheleasteffectonimproving theperformance offaultdiagnosis
models;TheuseofVAEfordataaugmentation issecondonlytothe
methodproposedinthispaperinimproving theperformance offault
diagnosismodels.Table7
Composition ofdatasetsfordifferentexperiments.
Experiment
CodeRealTraining
Samples
(eachclass)Generated
Training
Samples(each
class)Training
Samples
(eachclass)Testing
Samples
(eachclass)
Exp.1 20 0 20 180
Exp.2 20 50 70 180
Exp.3 20 200 220 180
Exp.4 20 400 420 180
Exp.5 0 600 600 180
Table8
TheaccuracyoffaultdiagnosisobtainedbydifferentmethodsusingtheCWRU
dataset(%)(mean±variance).
Models Exp.1Exp.2Exp.3Exp.4Exp.5
ACGAN VGG16 92.32
±0.9591.43
±0.4892.82
±0.8395.50
±0.3792.56
±0.17
Resnet50 75.32
±0.4573.57
±0.9489.83
±0.5492.82
±0.3091.47
±0.48
Densenet121 88.30
±0.8285.29
±0.4990.55
±0.3292.96
±0.7690.65
±0.39
VAE VGG16 92.32
±0.9592.43
±0.2394.50
±0.5396.53
±0.2396.53
±0.25
Resnet50 75.32
±0.4578.38
±0.5288.69
±0.8794.83
±0.1895.05
±0.12
Densenet121 88.30
±0.8288.61
±0.2693.71
±0.5995.61
±0.3695.30
±0.14
Guided
DiffusionVGG16 92.32
±0.9593.74
±0.9295.31
±0.7996.22
±0.2895.98
±0.27
Resnet50 75.32
±0.4588.05
±0.6991.27
±0.5897.41
±0.4293.63
±0.17
Densenet121 88.30
±0.8290.67
±0.2794.73
±0.4096.42
±0.2694.54
±0.23
CFGDMHD VGG16 92.32
±0.9595.42
±0.3498.73
±0.1099.17
±0.0499.22
±0.04
Resnet50 75.32
±0.4593.50
±0.4795.29
±0.4799.12
±0.2399.24
±0.06
Densenet121 88.30
±0.8293.53
±0.5595.70
±0.1998.36
±0.3298.72
±0.06
Fig.11.DIRGtestrig.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
12

4.3.Dataaugmentation andfaultdiagnosis experiments usingtheDIRG
dataset
4.3.1.Description andpreprocessing oftheDIRGbearingdataset
Thisdatasetwasobtainedthroughtherollingbearingtestrigofthe
dynamicandidentification researchgroup(DIRG),whichisspecifically
designedfortestinghigh-speed aviationbearings.Thetestrigisshown
inFig.11[9].TheDIRGbearingdatasetincludesthreetypesofbearing
healthstates:normal,innerringfailure,androllingelementfailure.The
sizeofthefaultypartis0.15mm,0.25mm,and0.45mm,sotherearea
totalof7categoriesofdata.ThefaultybearingislocatedatpositionB1,Table9
Detailedinformation ofDIRGdataset.
BearingCodeFaultLocation FaultSize(mm)NumberofSamples Label
NOR – – 300 0
IRF1 InnerRing 0.15 300 1
BF1 Ball 0.15 300 2
IRF2 InnerRing 0.25 300 3
BF2 Ball 0.25 300 4
IRF3 InnerRing 0.45 300 5
BF3 Ball 0.45 300 6
Fig.12.Generated sampleimagesoftheDIRGdataset.
Fig.13.ThegeneratedsamplesofDIRGdatasetanditstop3nearestneighborsinrealsamples.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
13

andthereisanaccelerometer atpositionsA1andA2thatcandetect
acceleration inthreedirections. Thesamplingfrequencyofthisaccel-
erometeris51,200Hz.Theexperimental datausedinthispaperisfrom
thethirdchanneloftheA1accelerometer, withashaftspeedof300Hz
andastaticloadof1800Nontheshaft.Thisdatasethas300samplesper
class,ofwhich100sampleswillbeusedfortestingand80sampleswill
beusedforCFGDMHD modeltraining.Eachsampleisobtainedfrom
1024datapointsthroughCWT.ThisdatasetisdifferentfromtheCWRU
dataset,andthisexperiment demonstrates theeffectiveness of
CFGDMHD inaviationbearingfaultdiagnosis.Thedetailedinformation
oftheDIRGdatasetisshowninTable9.
4.3.2.Qualityevaluation ofgenerated samplesandanalysisoffault
diagnosis resultsthroughdataaugmentation
Theexperimental processinthisdatasetisbasicallyconsistentwith
Fig.14.Visualization resultsofthefeaturesofrealandgeneratedsamplesfromamixedDIRGdataset.
Table10
DIRGdatasetwithdifferentmodelsgeneratessampleSSIMvalues.
Real-Real ACGAN VAE GuidedDiffusion CFGDMHD
Label00.8743 0.7821 0.6213 0.7768 0.8759
Label10.5985 0.4350 0.4566 0.4981 0.5612
Label20.7188 0.6348 0.4988 0.6020 0.6735
Label30.7762 0.6120 0.6862 0.5177 0.7590
Label40.5768 0.5146 0.4778 0.4432 0.5633
Label50.5674 0.3825 0.4432 0.4589 0.5028
Label60.8237 0.6872 0.5046 0.5369 0.8167
Fig.15.LinegraphsofSSIMvaluesfordifferentgeneratedsamplesintheDIRGdataset.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
14

thepreviousone.Addinganewdatasetforexperimental analysisisto
furtherdemonstrate theeffectiveness oftheproposedmethodinthis
paper,andtoobservewhethertheproposedmethodhasdifferentper-
formanceonotherdatasets.Afterpre-training andfine-tuning, the
CFGDMHD modelgenerates600samplesforeachclass.Fig.12dem-
onstratesthegeneratedsamplesoftheDIRGdataset.Itiseasytofind
thatthegeneratedsamplesarealsohighlysimilaranddiversetothereal
samples.Inaddition,theVGG16trainedwithrealsamplesisstillusedin
thisexperiment tomapthegeneratedsamplesandalltherealsamples
intothehigh-dimensional spacetofindthethreerealsamplesthatare
closesttothegeneratedsamples,andtheresultsareshowninFig.13.
Obviously, thegeneratedsamplesmaintainasimilarfeaturedistribution
totherealsamples,whilealsomaintaining maximum diversity.The
generatedsampleshavesufficientdifferences fromthethreeclosestreal
samples.Subsequently, weusedTSNEfeaturevisualization technology
todisplaythedatadistribution ofgeneratedandrealsamples.Asshown
inFig.14,therealsamplesandgeneratedsamplesofthesamecategory
arebasicallyclusteredinthesamearea,andthereisaclearboundary
betweenthefaultstatedataofthesevencategories. Throughqualitative
analysis,itwasfoundthatthesamplesgeneratedfromtheexperiment
ontheDIRGdatasethavehighqualityandrichdiversity,whichis
consistentwiththeresultsofthefirstexperiment.
Thequantitative analysisofthegeneratedsamplesinthisexperiment
alsoincludescomparisons ofSSIMvalues,ISvalues,andFIDvalues.The
generation modelsusedforcomparison arestillACGAN,VAE,and
GuidedDiffusion.Table10showstheSSIMvalues,Fig.15visuallyil-
lustratesthetrendchangesofSSIMandthedifferences amongdifferent
methods,andTable11showstheISandFIDvalues.TheSSIMvalueof
themethodproposedinthisarticleismoreinlinewiththeSSIMvalueof
Real-Real,withacloserdistanceof1.Thisalsoindicatesthatthemethod
proposedinthisarticlegeneratessamplesthataremoresimilartoreal
samplesonthisdataset.Inaddition,theISvaluesobtainedbythefour
modelsarenotsignificantly different,withCFGDMHD havinganIS
valueatleast4.54higherthanothermethods.TheFIDvalueofthe
CFGDMHD methodis7.61,whichisthesmallestamongthefour
methods.Thisindicatesthatthesamplequalityanddiversitygenerated
bythemethodproposedinthisarticlearesuperiortoothercomparative
models.
Finally,thesamplesgenerated bythesefourdataaugmentation
modelswillbeusedtoassistintrainingthreefaultdiagnosismodels.The
datasetcomposition ofthefaultdiagnosismodelisconsistent with
Table7,andtherearefiveexperiments withdifferentproportions of
generatedsamplesandrealsamplesinthetrainingset.
Theexperimental resultsareshowninTable12.Themethodproposedinthispaperstillperformsthebest,withthehighestaccuracy
of97.7%inExp.3,whichisalreadysuperiortothemajorityofexper-
imentalfaultdiagnosisresultsofotherdataaugmentation methods.
Whenthereisnorealdataasthetrainingset,theaccuracyoffault
diagnosisassistedbyCFGDMHD dataaugmentation isslightlylower
thantheexperimental accuracyinExp.4,butitismuchhigherthanthe
experimental resultsofotherdataaugmentation methods.Thissuggests
thatalimitednumberofrealsamplesmustbeincludedinthetraining
setwhenutilizingdataaugmentation techniques tohelptrainfault
diagnosismodelstomaximizethemodel ’sperformance. Inthisexperi-
ment,theCFGDMHD modelalsoshowedexceptional generation ability,
producing samplesofhighqualityandrichdiversityandofferingfresh
approaches totheissueoffew-shotdatainmechanical faultdiagnosis.
5.Conclusion
Theimbalance andscarcityofmechanical faultdatalimitthe
development ofdeeplearningmethodsinthefieldofintelligent fault
diagnosis. Dataaugmentation methodsareaneffectiveapproachto
addressfew-shotfaultdiagnosismethods,astheycanassistinimproving
theaccuracyofmodelfaultdiagnosis, ensuringthesafeandreliable
operation ofmechanical systems.Thispaperproposesthatthe
CFGDMHD modelcangeneratehigh-quality anddiversesamples,
effectively overcoming theshortcomings ofexistingdataaugmentation
methods.Themaininnovation ofthismethodliesintheintroduction of
anewgenerative modelfordataaugmentation tasksinmechanical fault
diagnosis;ProposedHDlosstomakethemodelmoresuitableforme-
chanicalfaultsamplegeneration tasks;Theparameter transfermethod
wasadoptedtosolvetheproblemofsuboptimal trainingofthegener-
ativemodelcausedbyasmallsamplesize;Wehavedevelopedamethod
thatcangeneratemultiplehigh-quality mechanical faultsamples
simultaneously withouttheneedforexternalclassifierguidance.
Wevalidatedtheeffectiveness oftheCFGDMHD methodusingthe
CWRUbearingdatasetandtheDIRGbearingdataset.Weconducted
comparative experiments usingACGAN,VAE,andGuidedDiffusionto
comparetheadvantages anddisadvantages ofdifferentdataaugmen-
tationmethodsingenerating faultsamples.Finally,generatesamplesto
assistVGG16,Resnet,andDensenet121 infaultdiagnosis,andcompare
theexperimental resultsofvariousdataaugmentation methodson
differentfaultdiagnosismodels.
Basedontheexperimental results,thefollowingconclusions canbe
drawn.
(1)TheCFGDMHD modelcaneffectively generatehigh-quality
samplesandgeneratesampleswithgooddiversity.Through
quantitative andqualitative analysis,theCFGDMHD modelis
superiortoothermethods.
(2)Comparing theexperimental accuracies ofdifferentdata
enhancement methodsassistingeachfaultdiagnosismodel,itcan
befoundthattheexperimental resultsofCFGDMHD data
augmentation methodarebetterthanothermethods.EvenifTable11
DIRGdatasetgeneration sampleswithdifferentmodelsISandFIDvalues.
ACGAN VAE GuidedDiffusion CFGDMHD
IS 21.98 23.14 20.72 27.68
FID 17.24 15.70 11.25 7.61
Table12
TheaccuracyoffaultdiagnosisobtainedbydifferentmethodsusingtheDIRGdataset(%)(mean±variance).
Models Exp.1 Exp.2 Exp.3 Exp.4 Exp.5
ACGAN VGG16 90.51±0.44 92.21±0.63 94.92±0.13 93.49±0.24 89.84±0.24
Resnet50 85.33±0.13 88.38±0.21 93.68±0.27 93.210.03 84.48±0.48
Densenet121 88.47±0.99 89.74±1.06 96.54±0.69 95.28±0.35 85.50±0.63
VAE VGG16 90.51±0.44 91.03±0.36 95.83±0.17 98.34±0.06 98.08±0.20
Resnet50 85.33±0.13 91.77±0.35 93.25±0.30 96.61±0.31 96.34±0.19
Densenet121 88.47±0.99 91.14±1.50 95.38±0.39 96.01±0.40 95.81±0.14
GuidedDiffusion VGG16 90.51±0.44 90.90±0.12 92.65±0.30 94.60±0.41 95.05±0.12
Resnet50 85.33±0.13 85.42±0.13 93.66±0.80 95.41±0.22 95.43±0.06
Densenet121 88.47±0.99 87.65±0.37 91.70±0.12 94.12±0.41 94.93±0.18
CFGDMHD VGG16 90.51 ±0.44 94.29 ±0.27 96.51±0.35 99.19 ±0.01 98.87 ±0.03
Resnet50 85.33±0.13 93.53±0.24 97.70 ±0.59 98.40±0.05 98.24±0.07
Densenet121 88.47±0.99 91.41±0.52 96.68±0.51 97.39±0.39 98.06±0.06Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
15

therearenorealsamplesinthetrainingset,thefaultdiagnosis
accuracystilldoesnotfluctuatetoomuch,whichindicatesthat
thesamplesgeneratedbyCFGDMHD areverysimilartothereal
samples.
(3)Observingthenearestneighborsofthegeneratedsamplesinreal
samplesindicatesthatthesamplesgenerated bythemethod
proposedinthispaperdonotduplicatethefeaturedistribution of
realsamples.Comparing theSSIM,IS,andFIDindicators of
GuidedDiffusionandCFGDMHD, CFGDMHD performsbetter.
TheaccuracyofCFGDMHD inallthreefaultdiagnosismodelsis
higherthanthatofGuidedDiffusion.Theaboveresultsindicate
thatourimprovedlossfunctiontosomeextentincreasesthedi-
versityofgeneratedsamples,andparameter transfertechnology
ofpre-training andfine-tuning arealsobeneficialforgenerating
bettersamples.
(4)Theproposedmethodcangeneratemoresamples,coveringa
widerrangeoffaulttypesandscenarios,therebyenhancing the
accuracyandgeneralization abilityofthefaultdiagnosismodel.
Ithelpstopromptlydetectandresolveequipment issues,
ensuringthesmoothandsafeoperationoftheequipment.
Overall,thedataaugmentation methodproposedinthispaperhas
excellentperformance insolvingfew-shotbearingfaultdiagnosis. In
futurework,wewillinvestigate theapplication oftheproposeddata
augmentation methodindifferentfaultycomponents, aswellasdata
augmentation incomplexworkingconditions, toimprovetheuniver-
salityofthemodel.
Dataavailability
Datawillbemadeavailableonrequest.
CRediTauthorship contribution statement
YuanWei:Writing –originaldraft,Methodology, Investigation,
Conceptualization. ZhijunXiao:Writing –originaldraft,Software,
Methodology, Formalanalysis. Xiangyan Chen:Writing –review &
editing,Resources. XiaohuiGu:Writing –review &editing.Kai-Uwe
Schr oder:Visualization.
Declaration ofcompeting interest
Theauthorsdeclarethattheyhavenoknowncompeting financial
interestsorpersonalrelationships thatcouldhaveappearedtoinfluence
theworkreportedinthispaper.
Acknowledgments
ThisworkwassupportedbytheNationalNaturalScienceFoundation
ofChina(Nos.11802168, 52075310), ChinaScholarship Council(No.
202006890138), andS&TProgramofHebei(225676162GH).
References
[1]ZhuZ,LeiY,QiG,etal.Areviewoftheapplication ofdeeplearninginintelligent
faultdiagnosisofrotatingmachinery. Measurement 2023;206:112346 .
[2]WeiY,XiaoZ,LiuS,etal.Anoveldataaugmentation andcomposite multi-scale
networkformechanical faultdiagnosis.IEEETransInstrumMeas2023;72:
3525912.
[3]ZhangW,WangZ,LiX.Blockchain-based decentralized federatedtransferlearning
methodology forcollaborative machinery faultdiagnosis.ReliabEngSystSaf2023;
229:108885 .
[4]WangJ,RenH,ShenC,etal.Multi-scale stylegenerative andadversarial
contrastive networksforsingledomaingeneralization faultdiagnosis.ReliabEng
SystSaf2024;243:109879 .
[5]JiaoJ,ZhaoM,LinJ,etal.Acomprehensive reviewonconvolutional neural
networkinmachinefaultdiagnosis.Neurocomputing 2020;417:36 –63.[6]WangB,WeiY,LiuS,etal.Unsupervised jointsubdomain adaptation networkfor
faultdiagnosis.IEEESensJ2022;22(9):8891 –903.
[7]XieT,XuQ,JiangC,etal.Thefaultfrequencypriorsfusiondeeplearning
framework withapplication tofaultdiagnosisofoffshorewindturbines.Renew
Energy2023;202:143 –53.
[8]ShiM,DingC,WangR,etal.Graphembedding deepbroadlearningsystemfor
dataimbalance faultdiagnosisofrotatingmachinery. ReliabEngSystSaf2023;
240:109601 .
[9]CaoH,NiuL,XiS,etal.Mechanical modeldevelopment ofrollingbearingrotor
systems:areview.MechSystSignalProcess2018;102:37 –58.
[10]HuangM,YinJ,YanS,etal.Afaultdiagnosismethodofbearingsbasedondeep
transferlearning.SimulModelPractTheory2023;112:102659 .
[11]XiaP,HuangY,TaoZ,etal.Adigitaltwin-enhanced semi-supervised framework
formotorfaultdiagnosisbasedonphase-contrastive currentdotpattern.Reliab
EngSystSaf2023;235:109256 .
[12]JiangZ,ZhangK,XiangL,etal.Atime-frequency spectralamplitude modulation
methodanditsapplications inrollingbearingfaultdiagnosis.MechSystSignal
Process2023;185:109832 .
[13]GawdeS,PatilS,KumarS,etal.Multi-fault diagnosisofindustrialrotating
machinesusingdata-driven approach:areviewoftwodecadesofresearch.Eng
ApplArtifIntell2023;123:106139 .
[14]LiuD,CuiL,WangH.Rotatingmachinery faultdiagnosisundertime-varying
speeds:areview.IEEESensJ2023;23(24):29969 –90.
[15]HanC,LuW,WangH,etal.Multistatefaultdiagnosisstrategyforbearingsbased
onanimprovedconvolutional sparsecodingwithprioriperiodicfiltergroup.Mech
SystSignalProcess2023;188:109995 .
[16]DaoF,ZengY,QianJ.Faultdiagnosisofhydro-turbine viatheincorporation of
bayesianalgorithmoptimizedCNN-LSTM neuralnetwork.Energy2024;290:
130326.
[17]ZhuQ,QianY,ZhangN,etal.Multi-scale Transformer-CNN domainadaptation
networkforcomplexprocessesfaultdiagnosis.JProcessControl2023;130:
103069.
[18]ShiP,WuS,XuX,etal.TSN:anovelintelligentfaultdiagnosismethodforbearing
withsmallsamplesundervariableworkingconditions. ReliabEngSystSaf2023;
240:109575 .
[19]TianJ,JiangY,ZhangJ,etal.Anoveldataaugmentation approachtofault
diagnosiswithclass-imbalance problem.ReliabEngSystSaf2024;243:109832 .
[20]SunQ,PengF,YuX,etal.Dataaugmentation strategyforpowerinverterfault
diagnosisbasedonwasserstein distanceandauxiliaryclassification generative
adversarial network.ReliabEngSystSaf2023;237:109360 .
[21]DaiZ,ZhaoL,WangK,etal.Generative adversarial networktoalleviate
information insufficiency inintelligent faultdiagnosisbygenerating continuations
ofsignals.ApplSoftComput2023;147:110784 .
[22]GuoQ,LiY,LiuY,etal.Dataaugmentation forintelligent mechanical fault
diagnosisbasedonlocalsharedmultiple-generator GAN.IEEESensJ2022;22(10):
9598 –609.
[23]LuoQ,YangW,HeJ,etal.Faultdiagnosismethodbasedontwo-stageGANfor
dataimbalance. IEEESensJ2022;22(22):21961 –73.
[24]ShaoL,LuN,JiangB,etal.Improvedgenerative adversarial networkswith
filteringmechanism forfaultdataaugmentation. IEEESensJ2023;23(13):
15176 –87.
[25]ZhongH,YuS,TrinhH,etal.Fine-tuning transferlearningbasedonDCGAN
integrated withself-attention andspectralnormalization forbearingfault
diagnosis.Measurement 2023;210:112421 .
[26]YanK,SuJ,HuangJ,etal.ChillerfaultdiagnosisbasedonVAE-enabled generative
adversarial networks.IEEETransAutomSciEng2020;19(1):387 –95.
[27]LiW,ZhongX,ShaoH,etal.Multi-mode dataaugmentation andfaultdiagnosisof
rotatingmachinery usingmodifiedACGANdesignedwithnewframework. Adv
EngInform2022;52:101552 .
[28]DhariwalP,NicholA.DiffusionmodelsbeatGansonimagesynthesis.AdvNeural
InfProcessSyst2021;34:8780 –94.
[29]Sohl-Dickstein J,WeissE,Maheswaranathan N,etal.Deepunsupervised learning
usingnonequilibrium thermodynamics. In:International Conference onMachine
LearningPMLR;2015.p.2256 –65.
[30]HoJ,JainA,AbbeelP.Denoisingdiffusionprobabilistic models.AdvNeuralInf
ProcessSyst2020;33:6840 –51.
[31]HoJ,SalimansT.Classifier-free diffusionguidance,arXivpreprintarXiv2022;
2207:12598 .
[32]NicholA,DhariwalP.Improveddenoisingdiffusionprobabilistic models.In:
International Conference onMachineLearningPMLR;2021.p.8162 –71.
[33]Russakovsky O,DengJ,SuH,etal.Imagenetlargescalevisualrecognition
challenge. IntJComputVis2015;115(3):211 –52.
[34]LessmeierC,KimothoJ,ZimmerD,etal.Conditionmonitoring ofbearingdamage
inelectromechanical drivesystemsbyusingmotorcurrentsignalsofelectric
motors:abenchmark datasetfordata-driven classification. In:SocietyEuropean
Conference PHM.3;2016.
[35]SmithWA,RandallRB.Rollingelementbearingdiagnostics usingtheCaseWestern
ReserveUniversity data:abenchmark study.MechSystSignalProcess2015;64-65:
100–31.
[36]DagaAP,FasanaA,Marchesiello S,etal.ThePolitecnico diTorinorollingbearing
testrig:description andanalysisofopenaccessdata.MechSystSignalProcess
2019;120:252 –73.Y.Weietal. Reliability  Engineering  and System  Safety  253 (2025)  110567
16

